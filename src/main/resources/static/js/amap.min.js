!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("vue")):"function"==typeof define&&define.amd?define("VueAMap",["vue"],e):"object"==typeof exports?exports.VueAMap=e(require("vue")):t.VueAMap=e(t.Vue)}("undefined"!=typeof self?self:this,function(t){return function(t){function e(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}};return t[r].call(o.exports,o,o.exports,e),o.l=!0,o.exports}var n={};return e.m=t,e.c=n,e.d=function(t,n,r){e.o(t,n)||Object.defineProperty(t,n,{configurable:!1,enumerable:!0,get:r})},e.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,"a",n),n},e.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},e.p="./",e(e.s=64)}([function(t,e,n){"use strict";var r=n(45),o=n.n(r),i=n(6),a=n(50),s=n(13),u=n(49),c=n(27);e.a={data:function(){return{unwatchFns:[]}},mounted:function(){var t=this;s.b&&s.b.load().then(function(){t.__contextReady&&t.__contextReady.call(t,t.convertProps())}),this.$amap=this.$amap||this.$parent.$amap,this.$amap?this.register():this.$on(u.a.AMAP_READY_EVENT,function(e){t.$amap=e,t.register()})},destroyed:function(){this.unregisterEvents(),this.$amapComponent&&(this.$amapComponent.setMap&&this.$amapComponent.setMap(null),this.$amapComponent.close&&this.$amapComponent.close(),this.$amapComponent.editor&&this.$amapComponent.editor.close(),this.unwatchFns.forEach(function(t){return t()}),this.unwatchFns=[])},methods:{getHandlerFun:function(t){return this.handlers&&this.handlers[t]?this.handlers[t]:this.$amapComponent["set"+o()(t)]||this.$amapComponent.setOptions},convertProps:function(){var t=this,e={};this.$amap&&(e.map=this.$amap);var n=this.$options.propsData,r=void 0===n?{}:n,o=this.propsRedirect;return Object.keys(r).reduce(function(n,i){var a=i,s=t.convertSignalProp(a,r[a]);return void 0===s?n:(o&&o[i]&&(a=o[a]),e[a]=s,n)},e)},convertSignalProp:function(t,e){var n="",r="";if(this.amapTagName)try{var a=o()(this.amapTagName).replace(/^El/,"");r=(c.default[a]||"").props[t].$type,n=i.a[r]}catch(t){}if(r&&n)return n(e);if(this.converters&&this.converters[t])return this.converters[t].call(this,e);var s=i.a[t];return s?s(e):e},registerEvents:function(){if(this.setEditorEvents&&this.setEditorEvents(),this.$options.propsData){if(this.$options.propsData.events)for(var t in this.events)a.a.addListener(this.$amapComponent,t,this.events[t]);if(this.$options.propsData.onceEvents)for(var e in this.onceEvents)a.a.addListenerOnce(this.$amapComponent,e,this.onceEvents[e])}},unregisterEvents:function(){a.a.clearListeners(this.$amapComponent)},setPropWatchers:function(){var t=this,e=this.propsRedirect,n=this.$options.propsData,r=void 0===n?{}:n;Object.keys(r).forEach(function(n){var r=n;e&&e[n]&&(r=e[n]);var o=t.getHandlerFun(r);if(o||"events"===n){var i=t.$watch(n,function(e){if("events"===n)return t.unregisterEvents(),void t.registerEvents();if(o&&o===t.$amapComponent.setOptions){var i;return o.call(t.$amapComponent,(i={},i[r]=t.convertSignalProp(n,e),i))}o.call(t.$amapComponent,t.convertSignalProp(n,e))});t.unwatchFns.push(i)}})},registerToManager:function(){var t=this.amapManager||this.$parent.amapManager;t&&void 0!==this.vid&&t.setComponent(this.vid,this.$amapComponent)},initProps:function(){var t=this;["editable","visible"].forEach(function(e){if(void 0!==t[e]){var n=t.getHandlerFun(e);n&&n.call(t.$amapComponent,t.convertSignalProp(e,t[e]))}})},printReactiveProp:function(){var t=this;Object.keys(this._props).forEach(function(e){t.$amapComponent["set"+o()(e)]&&console.log(e)})},register:function(){var t=this,e=this.__initComponent&&this.__initComponent(this.convertProps());e&&e.then?e.then(function(e){return t.registerRest(e)}):this.registerRest(e)},registerRest:function(t){!this.$amapComponent&&t&&(this.$amapComponent=t),this.registerEvents(),this.initProps(),this.setPropWatchers(),this.registerToManager(),this.events&&this.events.init&&this.events.init(this.$amapComponent,this.$amap,this.amapManager||this.$parent.amapManager)},$$getInstance:function(){return this.$amapComponent}}}},function(t,e,n){"use strict";function r(t,e,n,r,o,i,a,s){t=t||{};var u=typeof t.default;"object"!==u&&"function"!==u||(t=t.default);var c="function"==typeof t?t.options:t;e&&(c.render=e,c.staticRenderFns=n,c._compiled=!0),r&&(c.functional=!0),i&&(c._scopeId=i);var p;if(a?(p=function(t){t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,t||"undefined"==typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),o&&o.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(a)},c._ssrRegister=p):o&&(p=s?function(){o.call(this,this.$root.$options.shadowRoot)}:o),p)if(c.functional){c._injectStyles=p;var f=c.render;c.render=function(t,e){return p.call(e),f(t,e)}}else{var l=c.beforeCreate;c.beforeCreate=l?[].concat(l,p):[p]}return{exports:t,options:c}}e.a=r},function(t,e,n){var r=n(30)("wks"),o=n(14),i=n(3).Symbol,a="function"==typeof i;(t.exports=function(t){return r[t]||(r[t]=a&&i[t]||(a?i:o)("Symbol."+t))}).store=r},function(t,e){var n=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},function(t,e){t.exports=function(t){return"object"==typeof t?null!==t:"function"==typeof t}},function(t,e,n){t.exports=!n(15)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},function(t,e,n){"use strict";function r(t){return new AMap.Pixel(t[0],t[1])}function o(t){return new AMap.Size(t[0],t[1])}function i(t){return Array.isArray(t)?t:[t.getX(),t.getY()]}function a(t){return new AMap.LngLat(t[0],t[1])}function s(t){if(t)return Array.isArray(t)?t.slice():[t.getLng(),t.getLat()]}function u(t){return new AMap.Bounds(a(t[0]),a(t[1]))}e.e=r,e.c=i,e.d=a,e.b=s,n.d(e,"a",function(){return c});var c={position:a,offset:r,bounds:u,LngLat:a,Pixel:r,Size:o,Bounds:u}},function(t,e,n){var r=n(3),o=n(8),i=n(11),a=n(14)("src"),s=Function.toString,u=(""+s).split("toString");n(16).inspectSource=function(t){return s.call(t)},(t.exports=function(t,e,n,s){var c="function"==typeof n;c&&(i(n,"name")||o(n,"name",e)),t[e]!==n&&(c&&(i(n,a)||o(n,a,t[e]?""+t[e]:u.join(String(e)))),t===r?t[e]=n:s?t[e]?t[e]=n:o(t,e,n):(delete t[e],o(t,e,n)))})(Function.prototype,"toString",function(){return"function"==typeof this&&this[a]||s.call(this)})},function(t,e,n){var r=n(9),o=n(20);t.exports=n(5)?function(t,e,n){return r.f(t,e,o(1,n))}:function(t,e,n){return t[e]=n,t}},function(t,e,n){var r=n(10),o=n(31),i=n(33),a=Object.defineProperty;e.f=n(5)?Object.defineProperty:function(t,e,n){if(r(t),e=i(e,!0),r(n),o)try{return a(t,e,n)}catch(t){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(t[e]=n.value),t}},function(t,e,n){var r=n(4);t.exports=function(t){if(!r(t))throw TypeError(t+" is not an object!");return t}},function(t,e){var n={}.hasOwnProperty;t.exports=function(t,e){return n.call(t,e)}},function(t,e){t.exports={}},function(t,e,n){"use strict";n.d(e,"a",function(){return s}),n.d(e,"b",function(){return a});var r=n(97),o=n(19),i=n.n(o),a=null,s=function(t){i.a.prototype.$isServer||a||(a||(a=new r.a(t)),a.load())}},function(t,e){var n=0,r=Math.random();t.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++n+r).toString(36))}},function(t,e){t.exports=function(t){try{return!!t()}catch(t){return!0}}},function(t,e){var n=t.exports={version:"2.5.5"};"number"==typeof __e&&(__e=n)},function(t,e,n){var r=n(71);t.exports=function(t,e,n){if(r(t),void 0===e)return t;switch(n){case 1:return function(n){return t.call(e,n)};case 2:return function(n,r){return t.call(e,n,r)};case 3:return function(n,r,o){return t.call(e,n,r,o)}}return function(){return t.apply(e,arguments)}}},function(t,e,n){var r=n(75),o=n(22);t.exports=function(t){return r(o(t))}},function(e,n){e.exports=t},function(t,e){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},function(t,e){var n=Math.ceil,r=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(t>0?r:n)(t)}},function(t,e){t.exports=function(t){if(void 0==t)throw TypeError("Can't call method on  "+t);return t}},function(t,e,n){"use strict";var r=n(70),o=n(34),i=n(7),a=n(8),s=n(12),u=n(72),c=n(25),p=n(79),f=n(2)("iterator"),l=!([].keys&&"next"in[].keys()),d=function(){return this};t.exports=function(t,e,n,h,v,m,y){u(n,e,h);var g,b,_,x=function(t){if(!l&&t in M)return M[t];switch(t){case"keys":case"values":return function(){return new n(this,t)}}return function(){return new n(this,t)}},C=e+" Iterator",w="values"==v,$=!1,M=t.prototype,E=M[f]||M["@@iterator"]||v&&M[v],A=E||x(v),O=v?w?x("entries"):A:void 0,S="Array"==e?M.entries||E:E;if(S&&(_=p(S.call(new t)))!==Object.prototype&&_.next&&(c(_,C,!0),r||"function"==typeof _[f]||a(_,f,d)),w&&E&&"values"!==E.name&&($=!0,A=function(){return E.call(this)}),r&&!y||!l&&!$&&M[f]||a(M,f,A),s[e]=A,s[C]=d,v)if(g={values:w?A:x("values"),keys:m?A:x("keys"),entries:O},y)for(b in g)b in M||i(M,b,g[b]);else o(o.P+o.F*(l||$),e,g);return g}},function(t,e,n){var r=n(30)("keys"),o=n(14);t.exports=function(t){return r[t]||(r[t]=o(t))}},function(t,e,n){var r=n(9).f,o=n(11),i=n(2)("toStringTag");t.exports=function(t,e,n){t&&!o(t=n?t:t.prototype,i)&&r(t,i,{configurable:!0,value:e})}},function(t,e,n){"use strict";var r=n(50);e.a={methods:{setEditorEvents:function(){var t=this;if(this.$amapComponent.editor&&this.events){var e=["addnode","adjust","removenode","end","move"],n={};Object.keys(this.events).forEach(function(r){-1!==e.indexOf(r)&&(n[r]=t.events[r])}),Object.keys(n).forEach(function(e){r.a.addListener(t.$amapComponent.editor,e,n[e])})}}}}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=(n(65),n(45)),o=n.n(r),i=n(13),a=n(100),s=n(106),u=n(107),c=n(111),p=n(113),f=n(115),l=n(116),d=n(118),h=n(120),v=n(122),m=n(124),y=n(126),g=n(128),b=n(130),_=n(131);n.d(e,"AMapManager",function(){return b.a}),n.d(e,"initAMapApiLoader",function(){return i.a}),n.d(e,"createCustomComponent",function(){return _.a}),n.d(e,"lazyAMapApiLoaderInstance",function(){return i.b});var x=[a.a,s.a,u.a,c.a,p.a,f.a,d.a,l.a,h.a,v.a,m.a,y.a,g.a],C={initAMapApiLoader:i.a,AMapManager:b.a};C.install=function(t){C.installed||(t.config.optionMergeStrategies.deferredReady=t.config.optionMergeStrategies.created,x.map(function(e){t.component(e.name,e),C[o()(e.name).replace(/^El/,"")]=e}))};"undefined"!=typeof window&&window.Vue&&function t(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];t.installed||C.install(e)}(window.Vue),e.default=C},function(t,e,n){var r=n(29),o=n(2)("toStringTag"),i="Arguments"==r(function(){return arguments}()),a=function(t,e){try{return t[e]}catch(t){}};t.exports=function(t){var e,n,s;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=a(e=Object(t),o))?n:i?r(e):"Object"==(s=r(e))&&"function"==typeof e.callee?"Arguments":s}},function(t,e){var n={}.toString;t.exports=function(t){return n.call(t).slice(8,-1)}},function(t,e,n){var r=n(3),o=r["__core-js_shared__"]||(r["__core-js_shared__"]={});t.exports=function(t){return o[t]||(o[t]={})}},function(t,e,n){t.exports=!n(5)&&!n(15)(function(){return 7!=Object.defineProperty(n(32)("div"),"a",{get:function(){return 7}}).a})},function(t,e,n){var r=n(4),o=n(3).document,i=r(o)&&r(o.createElement);t.exports=function(t){return i?o.createElement(t):{}}},function(t,e,n){var r=n(4);t.exports=function(t,e){if(!r(t))return t;var n,o;if(e&&"function"==typeof(n=t.toString)&&!r(o=n.call(t)))return o;if("function"==typeof(n=t.valueOf)&&!r(o=n.call(t)))return o;if(!e&&"function"==typeof(n=t.toString)&&!r(o=n.call(t)))return o;throw TypeError("Can't convert object to primitive value")}},function(t,e,n){var r=n(3),o=n(16),i=n(8),a=n(7),s=n(17),u=function(t,e,n){var c,p,f,l,d=t&u.F,h=t&u.G,v=t&u.S,m=t&u.P,y=t&u.B,g=h?r:v?r[e]||(r[e]={}):(r[e]||{}).prototype,b=h?o:o[e]||(o[e]={}),_=b.prototype||(b.prototype={});h&&(n=e);for(c in n)p=!d&&g&&void 0!==g[c],f=(p?g:n)[c],l=y&&p?s(f,r):m&&"function"==typeof f?s(Function.call,f):f,g&&a(g,c,f,t&u.U),b[c]!=f&&i(b,c,l),m&&_[c]!=f&&(_[c]=f)};r.core=o,u.F=1,u.G=2,u.S=4,u.P=8,u.B=16,u.W=32,u.U=64,u.R=128,t.exports=u},function(t,e,n){var r=n(10),o=n(73),i=n(38),a=n(24)("IE_PROTO"),s=function(){},u=function(){var t,e=n(32)("iframe"),r=i.length;for(e.style.display="none",n(78).appendChild(e),e.src="javascript:",t=e.contentWindow.document,t.open(),t.write("<script>document.F=Object<\/script>"),t.close(),u=t.F;r--;)delete u.prototype[i[r]];return u()};t.exports=Object.create||function(t,e){var n;return null!==t?(s.prototype=r(t),n=new s,s.prototype=null,n[a]=t):n=u(),void 0===e?n:o(n,e)}},function(t,e,n){var r=n(74),o=n(38);t.exports=Object.keys||function(t){return r(t,o)}},function(t,e,n){var r=n(21),o=Math.min;t.exports=function(t){return t>0?o(r(t),9007199254740991):0}},function(t,e){t.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(t,e){t.exports=function(t,e){return{value:e,done:!!t}}},function(t,e,n){var r=n(7);t.exports=function(t,e,n){for(var o in e)r(t,o,e[o],n);return t}},function(t,e){t.exports=function(t,e,n,r){if(!(t instanceof e)||void 0!==r&&r in t)throw TypeError(n+": incorrect invocation!");return t}},function(t,e,n){var r=n(17),o=n(86),i=n(87),a=n(10),s=n(37),u=n(88),c={},p={},e=t.exports=function(t,e,n,f,l){var d,h,v,m,y=l?function(){return t}:u(t),g=r(n,f,e?2:1),b=0;if("function"!=typeof y)throw TypeError(t+" is not iterable!");if(i(y)){for(d=s(t.length);d>b;b++)if((m=e?g(a(h=t[b])[0],h[1]):g(t[b]))===c||m===p)return m}else for(v=y.call(t);!(h=v.next()).done;)if((m=o(v,g,h.value,e))===c||m===p)return m};e.BREAK=c,e.RETURN=p},function(t,e,n){var r=n(14)("meta"),o=n(4),i=n(11),a=n(9).f,s=0,u=Object.isExtensible||function(){return!0},c=!n(15)(function(){return u(Object.preventExtensions({}))}),p=function(t){a(t,r,{value:{i:"O"+ ++s,w:{}}})},f=function(t,e){if(!o(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!i(t,r)){if(!u(t))return"F";if(!e)return"E";p(t)}return t[r].i},l=function(t,e){if(!i(t,r)){if(!u(t))return!0;if(!e)return!1;p(t)}return t[r].w},d=function(t){return c&&h.NEED&&u(t)&&!i(t,r)&&p(t),t},h=t.exports={KEY:r,NEED:!1,fastKey:f,getWeak:l,onFreeze:d}},function(t,e,n){var r=n(4);t.exports=function(t,e){if(!r(t)||t._t!==e)throw TypeError("Incompatible receiver, "+e+" required!");return t}},function(t,e,n){"use strict";var r=n(96);t.exports=function(){var t=r.apply(r,arguments);return t.charAt(0).toUpperCase()+t.slice(1)}},function(t,e){function n(t,e){var n=t[1]||"",o=t[3];if(!o)return n;if(e&&"function"==typeof btoa){var i=r(o);return[n].concat(o.sources.map(function(t){return"/*# sourceURL="+o.sourceRoot+t+" */"})).concat([i]).join("\n")}return[n].join("\n")}function r(t){return"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(t))))+" */"}t.exports=function(t){var e=[];return e.toString=function(){return this.map(function(e){var r=n(e,t);return e[2]?"@media "+e[2]+"{"+r+"}":r}).join("")},e.i=function(t,n){"string"==typeof t&&(t=[[null,t,""]]);for(var r={},o=0;o<this.length;o++){var i=this[o][0];"number"==typeof i&&(r[i]=!0)}for(o=0;o<t.length;o++){var a=t[o];"number"==typeof a[0]&&r[a[0]]||(n&&!a[2]?a[2]=n:n&&(a[2]="("+a[2]+") and ("+n+")"),e.push(a))}},e}},function(t,e,n){function r(t){for(var e=0;e<t.length;e++){var n=t[e],r=p[n.id];if(r){r.refs++;for(var o=0;o<r.parts.length;o++)r.parts[o](n.parts[o]);for(;o<n.parts.length;o++)r.parts.push(i(n.parts[o]));r.parts.length>n.parts.length&&(r.parts.length=n.parts.length)}else{for(var a=[],o=0;o<n.parts.length;o++)a.push(i(n.parts[o]));p[n.id]={id:n.id,refs:1,parts:a}}}}function o(){var t=document.createElement("style");return t.type="text/css",f.appendChild(t),t}function i(t){var e,n,r=document.querySelector('style[data-vue-ssr-id~="'+t.id+'"]');if(r){if(h)return v;r.parentNode.removeChild(r)}if(m){var i=d++;r=l||(l=o()),e=a.bind(null,r,i,!1),n=a.bind(null,r,i,!0)}else r=o(),e=s.bind(null,r),n=function(){r.parentNode.removeChild(r)};return e(t),function(r){if(r){if(r.css===t.css&&r.media===t.media&&r.sourceMap===t.sourceMap)return;e(t=r)}else n()}}function a(t,e,n,r){var o=n?"":r.css;if(t.styleSheet)t.styleSheet.cssText=y(e,o);else{var i=document.createTextNode(o),a=t.childNodes;a[e]&&t.removeChild(a[e]),a.length?t.insertBefore(i,a[e]):t.appendChild(i)}}function s(t,e){var n=e.css,r=e.media,o=e.sourceMap;if(r&&t.setAttribute("media",r),o&&(n+="\n/*# sourceURL="+o.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(o))))+" */"),t.styleSheet)t.styleSheet.cssText=n;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(n))}}var u="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!u)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var c=n(103),p={},f=u&&(document.head||document.getElementsByTagName("head")[0]),l=null,d=0,h=!1,v=function(){},m="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());t.exports=function(t,e,n){h=n;var o=c(t,e);return r(o),function(e){for(var n=[],i=0;i<o.length;i++){var a=o[i],s=p[a.id];s.refs--,n.push(s)}e?(o=c(t,e),r(o)):o=[];for(var i=0;i<n.length;i++){var s=n[i];if(0===s.refs){for(var u=0;u<s.parts.length;u++)s.parts[u]();delete p[s.id]}}}};var y=function(){var t=[];return function(e,n){return t[e]=n,t.filter(Boolean).join("\n")}}()},function(t,e,n){"use strict";var r=n(104),o=n(49),i=n(6),a=n(0),s=n(13),u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};e.a={name:"el-amap",mixins:[a.a],props:["viewMode","skyColor","rotateEnable","pitch","buildingAnimation","pitchEnable","vid","events","center","zoom","draggEnable","level","zooms","lang","defaultCursor","crs","animateEnable","isHotspot","defaultLayer","rotateEnable","resizeEnable","showIndoorMap","indoorMap","expandZoomRange","dragEnable","zoomEnable","doubleClickZoom","keyboardEnable","jogEnable","scrollWheel","touchZoom","mapStyle","plugin","features","amapManager"],beforeCreate:function(){this._loadPromise=s.b.load()},destroyed:function(){this.$amap&&this.$amap.destroy()},computed:{plugins:function(){var t=[],e=/^AMap./,n=function(t){return e.test(t)?t:"AMap."+t},r=function(t){return t.replace(e,"")};return"string"==typeof this.plugin?t.push({pName:n(this.plugin),sName:r(this.plugin)}):this.plugin instanceof Array&&(t=this.plugin.map(function(t){var e={};return"string"==typeof t?e={pName:n(t),sName:r(t)}:(t.pName=n(t.pName),t.sName=r(t.pName),e=t),e})),t}},data:function(){return{converters:{center:function(t){return Object(i.d)(t)}},handlers:{zoomEnable:function(t){this.setStatus({zoomEnable:t})},dragEnable:function(t){this.setStatus({dragEnable:t})},rotateEnable:function(t){this.setStatus({rotateEnable:t})}}}},mounted:function(){this.createMap()},addEvents:function(){var t=this;this.$amapComponent.on("moveend",function(){var e=t.$amapComponent.getCenter();t.center=[e.getLng(),e.getLat()]})},methods:{addPlugins:function(){var t=this.plugins.filter(function(t){return!AMap[t.sName]});return t&&t.length?this.$amapComponent.plugin(t,this.addMapControls):this.addMapControls()},addMapControls:function(){var t=this;this.plugins&&this.plugins.length&&(this.$plugins=this.$plugins||{},this.plugins.forEach(function(e){var n=t.convertAMapPluginProps(e),r=t.$plugins[n.pName]=new AMap[n.sName](n);if(t.$amapComponent.addControl(r),e.events)for(var o in e.events){var i=e.events[o];"init"===o?i(r):AMap.event.addListener(r,o,i)}}))},convertAMapPluginProps:function(t){if("object"===(void 0===t?"undefined":u(t))&&t.pName){switch(t.pName){case"AMap.ToolBar":case"AMap.Scale":t.offset&&t.offset instanceof Array&&(t.offset=Object(i.e)(t.offset))}return t}return""},setStatus:function(t){this.$amap.setStatus(t)},createMap:function(){var t=this;this._loadPromise.then(function(){var e=t.$el.querySelector(".el-vue-amap"),n=t.vid||Object(r.a)();e.id=n,t.$amap=t.$amapComponent=new AMap.Map(n,t.convertProps()),t.amapManager&&t.amapManager.setMap(t.$amap),t.$emit(o.a.AMAP_READY_EVENT,t.$amap),t.$children.forEach(function(e){e.$emit(o.a.AMAP_READY_EVENT,t.$amap)}),t.plugins&&t.plugins.length&&t.addPlugins()})},$$getCenter:function(){return this.$amap?Object(i.b)(this.$amap.getCenter()):Object(i.b)(this.center)}}}},function(t,e,n){"use strict";e.a={AMAP_READY_EVENT:"AMAP_READY_EVENT"}},function(t,e,n){"use strict";function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var o=void 0,i=function(){function t(){r(this,t),this._listener=new Map}return t.prototype.addListener=function(t,e,n,r){if(!AMap.event)throw new Error("please wait for Map API load");var o=AMap.event.addListener(t,e,n,r);this._listener.get(t)||this._listener.set(t,{});var i=this._listener.get(t);i[e]||(i[e]=[]),i[e].push(o)},t.prototype.removeListener=function(t,e,n){if(!AMap.event)throw new Error("please wait for Map API load");if(this._listener.get(t)&&this._listener.get(t)[e]){var r=this._listener.get(t)[e];if(n){var o=r.indexOf(n);AMap.event.removeListener(r[o]),r.splice(o,1)}else r.forEach(function(t){AMap.event.removeListener(t)}),this._listener.get(t)[e]=[]}},t.prototype.addListenerOnce=function(t,e,n,r){return AMap.event.addListenerOnce(t,e,n,r)},t.prototype.trigger=function(t,e,n){return AMap.event.trigger(t,e,n)},t.prototype.clearListeners=function(t){var e=this,n=this._listener.get(t);n&&Object.keys(n).map(function(n){e.removeListener(t,n)})},t}();o=o||new i,e.a=o},function(t,e,n){"use strict";var r=n(0),o=n(6),i=n(52),a=n(19),s=n.n(a),u="el-amap-marker";e.a={name:u,mixins:[r.a],props:["vid","position","offset","icon","content","topWhenClick","bubble","draggable","raiseOnDrag","cursor","visible","zIndex","angle","autoRotation","animation","shadow","title","clickable","shape","extData","label","events","onceEvents","template","vnode","contentRender"],data:function(){var t=this;return{$tagName:u,withSlots:!1,tmpVM:null,propsRedirect:{template:"content",vnode:"content",contentRender:"content"},converters:{shape:function(t){return new AMap.MarkerShape(t)},shadow:function(t){return new AMap.Icon(t)},template:function(e){var n=Object(i.a)(e,t);return this.$customContent=n,n.$el},vnode:function(e){var n="function"==typeof e?e(t):e,r=Object(i.c)(n);return this.$customContent=r,r.$el},contentRender:function(e){var n=Object(i.b)(e,t);return this.$customContent=n,n.$el},label:function(t){var e=t.content,n=void 0===e?"":e,r=t.offset,i=void 0===r?[0,0]:r;return{content:n,offset:Object(o.e)(i)}}},handlers:{zIndex:function(t){this.setzIndex(t)},visible:function(t){!1===t?this.hide():this.show()}}}},created:function(){this.tmpVM=new s.a({data:function(){return{node:""}},render:function(t){var e=this.node;return t("div",{ref:"node"},Array.isArray(e)?e:[e])}}).$mount()},methods:{__initComponent:function(t){this.$slots.default&&this.$slots.default.length&&(t.content=this.tmpVM.$refs.node),this.$amapComponent=new AMap.Marker(t)},$$getExtData:function(){return this.$amapComponent.getExtData()},$$getPosition:function(){return Object(o.b)(this.$amapComponent.getPosition())},$$getOffset:function(){return Object(o.c)(this.$amapComponent.getOffset())}},render:function(t){var e=this.$slots.default||[];return e.length&&(this.tmpVM.node=e),null},destroyed:function(){this.tmpVM.$destroy(),this.$customContent&&this.$customContent.$destroy&&this.$customContent.$destroy()}}},function(t,e,n){"use strict";n.d(e,"a",function(){return a}),n.d(e,"c",function(){return s}),n.d(e,"b",function(){return u});var r=n(19),o=n.n(r),i=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},a=function(t,e){var n=["methods","computed","data","filters"],r={},a=o.a.compile(t);n.forEach(function(t){r[t]=e.$parent.$parent.$options[t],"data"===t&&"function"==typeof r[t]&&(r[t]=r[t]())});var s=new o.a(i({},r,a));return s.$mount(),s},s=function(t){var e=new o.a({render:function(e){return e("div",t)}});return e.$mount(),e},u=function(t,e){var n=new o.a({render:function(n){return t(n,e)}});return n.$mount(),n}},function(t,e,n){"use strict";var r=n(0),o=n(13);e.a={name:"el-amap-search-box",mixins:[r.a],props:["searchOption","onSearchResult","events","default"],data:function(){return{keyword:this.default||"",tips:[],selectedTip:-1,loaded:!1,adcode:null}},mounted:function(){var t=this;o.b.load().then(function(){t.loaded=!0,t._onSearchResult=t.onSearchResult,t.events&&t.events.init&&t.events.init({autoComplete:t._autoComplete,placeSearch:t._placeSearch})})},computed:{_autoComplete:function(){if(this.loaded)return new AMap.Autocomplete(this.searchOption||{})},_placeSearch:function(){if(this.loaded)return new AMap.PlaceSearch(this.searchOption||{})}},methods:{autoComplete:function(){var t=this;this.keyword&&this._autoComplete&&this._autoComplete.search(this.keyword,function(e,n){"complete"===e&&(t.tips=n.tips)})},search:function(){var t=this;if(this.tips=[],this._placeSearch){var e=null;e=this.searchOption.citylimit&&this.searchOption.city?this.searchOption.city:this.adcode,this._placeSearch.setCity(e||this.searchOption.city),this._placeSearch.search(this.keyword,function(e,n){if(n&&n.poiList&&n.poiList.count){var r=n.poiList.pois,o=r.map(function(t){return t.lat=t.location.lat,t.lng=t.location.lng,t});t._onSearchResult(o)}else if(void 0===n.poiList)throw new Error(n)})}},changeTip:function(t){this.adcode=t.adcode,this.keyword=t.name,this.search()},selectTip:function(t){"up"===t&&this.selectedTip>0?(this.selectedTip-=1,this.keyword=this.tips[this.selectedTip].name,this.adcode=this.tips[this.selectedTip].adcode):"down"===t&&this.selectedTip+1<this.tips.length&&(this.selectedTip+=1,this.keyword=this.tips[this.selectedTip].name,this.adcode=this.tips[this.selectedTip].adcode)}}}},function(t,e,n){"use strict";var r=n(0),o=n(6),i=n(26);e.a={name:"el-amap-circle",mixins:[r.a,i.a],props:["vid","zIndex","center","bubble","radius","strokeColor","strokeOpacity","strokeWeight","editable","fillColor","fillOpacity","strokeStyle","extData","strokeDasharray","events","visible","extData","onceEvents"],data:function(){return{converters:{center:function(t){return Object(o.d)(t)}},handlers:{zIndex:function(t){this.setzIndex(t)},visible:function(t){!1===t?this.hide():this.show()},editable:function(t){!0===t?this.editor.open():this.editor.close()}}}},methods:{__initComponent:function(t){this.$amapComponent=new AMap.Circle(t),this.$amapComponent.editor=new AMap.CircleEditor(this.$amap,this.$amapComponent)},$$getCenter:function(){return Object(o.b)(this.$amapComponent.getCenter())}}}},function(t,e,n){"use strict";var r=n(0);e.a={name:"el-amap-ground-image",mixins:[r.a],props:["vid","clickable","opacity","url","bounds","visible","events","onceEvents"],destroyed:function(){this.$amapComponent.setMap(null)},data:function(){return{converters:{},handlers:{visible:function(t){!1===t?this.setMap(null):this.setMap(this.$amap)}}}},methods:{__initComponent:function(t){this.$amapComponent=new AMap.ImageLayer(t)}}}},function(t,e,n){"use strict";var r=n(6),o=n(0),i=n(52),a=n(19),s=n.n(a);e.a={name:"el-amap-info-window",mixins:[o.a],props:["vid","isCustom","autoMove","closeWhenClickMap","content","size","offset","position","showShadow","visible","events","template","vnode","contentRender"],data:function(){var t=this;return{withSlots:!1,tmpVM:null,propsRedirect:{template:"content",vnode:"content",contentRender:"content"},converters:{template:function(e){var n=Object(i.a)(e,t);return this.$customContent=n,n.$el},vnode:function(e){var n="function"==typeof e?e(t):e,r=Object(i.c)(n);return this.$customContent=r,r.$el},contentRender:function(e){var n=Object(i.b)(e,t);return this.$customContent=n,n.$el}},handlers:{visible:function(e){var n=this.getPosition();n&&(!1===e?this.close():this.open(t.$amap,[n.lng,n.lat]))},template:function(t){this.setContent(t)}}}},created:function(){this.tmpVM=new s.a({data:function(){return{node:""}},render:function(t){var e=this.node;return t("div",{ref:"node"},Array.isArray(e)?e:[e])}}).$mount()},destroyed:function(){this.$amapComponent.close(),this.tmpVM.$destroy(),this.$customContent&&this.$customContent.$destroy&&this.$customContent.$destroy()},methods:{__initComponent:function(t){this.$slots.default&&this.$slots.default.length&&(t.content=this.tmpVM.$refs.node),delete t.map,this.$amapComponent=new AMap.InfoWindow(t),!1!==this.visible&&this.$amapComponent.open(this.$amap,Object(r.d)(this.position))}},render:function(t){var e=this.$slots.default||[];return e.length&&(this.tmpVM.node=e),null}}},function(t,e,n){"use strict";var r=n(0),o=n(26),i=n(6);e.a={name:"el-amap-polyline",mixins:[r.a,o.a],props:["vid","zIndex","visible","editable","bubble","geodesic","isOutline","outlineColor","path","strokeColor","strokeOpacity","strokeWeight","strokeStyle","strokeDasharray","events","extData","onceEvents","lineJoin"],data:function(){return{converters:{},handlers:{visible:function(t){!1===t?this.hide():this.show()},editable:function(t){!0===t?this.editor.open():this.editor.close()}}}},methods:{__initComponent:function(t){this.$amapComponent=new AMap.Polyline(t),this.$amapComponent.editor=new AMap.PolyEditor(this.$amap,this.$amapComponent)},$$getPath:function(){return this.$amapComponent.getPath().map(i.b)},$$getBounds:function(){return this.$amapComponent.getBounds()},$$getExtData:function(){return this.$amapComponent.getExtData()}}}},function(t,e,n){"use strict";var r=n(0),o=n(26),i=n(6);e.a={name:"el-amap-polygon",mixins:[r.a,o.a],props:["vid","zIndex","path","bubble","strokeColor","strokeOpacity","strokeWeight","fillColor","editable","fillOpacity","extData","strokeStyle","visible","strokeDasharray","events","onceEvents","draggable"],data:function(){return{converters:{},handlers:{visible:function(t){!1===t?this.hide():this.show()},zIndex:function(t){this.setOptions({zIndex:t})},editable:function(t){!0===t?this.editor.open():this.editor.close()}}}},methods:{__initComponent:function(){var t=this.convertProps();this.$amapComponent=new AMap.Polygon(t),this.$amapComponent.editor=new AMap.PolyEditor(this.$amap,this.$amapComponent)},$$getPath:function(){return this.$amapComponent.getPath().map(i.b)},$$getExtData:function(){return this.$amapComponent.getExtData()},$$contains:function(t){return Array.isArray(t)&&(t=new AMap.LngLat(t[0],t[1])),this.$amapComponent.getBounds().contains(t)}}}},function(t,e,n){"use strict";var r=n(0),o="el-amap-text";e.a={name:o,mixins:[r.a],props:{vid:{type:String,default:""},text:{type:String,default:""},textAlign:{type:String,default:""},verticalAlign:{type:String,default:""},position:{type:Array,default:function(){return[0,0]},$type:"LngLat"},offset:{type:Array,default:function(){return[0,0]},$type:"Pixel"},topWhenClick:{type:Boolean,default:function(){return!1}},bubble:{type:Boolean,default:function(){return!1}},draggable:{type:Boolean,default:function(){return!1}},raiseOnDrag:{type:Boolean,default:function(){return!1}},cursor:{type:String,default:function(){return""}},visible:{type:Boolean,default:function(){return!0}},zIndex:{type:Number,default:function(){return 100}},angle:{type:Number,default:function(){return 0}},autoRotation:{type:Boolean,default:function(){return!1}},animation:{type:String,default:function(){return"“AMAP_ANIMATION_NONE”"}},shadow:{type:Object,default:function(){return{}},$type:"Icon"},title:{type:String,default:function(){return""}},clickable:{type:Boolean,default:!0},events:{type:Object,default:function(){return{}}}},data:function(){return{converters:{},handlers:{zIndex:function(t){this.setzIndex(t)},visible:function(t){!1===t?this.hide():this.show()}},amapTagName:o}},methods:{__initComponent:function(t){this.$amapComponent=new AMap.Text(t)}}}},function(t,e,n){"use strict";var r=n(0),o="el-amap-bezier-curve";e.a={name:o,mixins:[r.a],props:{vid:{type:String},path:{type:Array},strokeColor:{type:String},strokeOpacity:{type:Number},strokeWeight:{type:Number,default:function(){return 1}},strokeStyle:{type:String},strokeDasharray:{type:Array},zIndex:{type:Number},showDir:{type:Boolean},bubble:{type:Boolean},cursor:{type:String},outlineColor:{type:Boolean},isOutline:{type:Boolean},visible:{type:Boolean,default:!0},events:{type:Object,default:function(){return{}}}},data:function(){return{converters:{},handlers:{zIndex:function(t){this.setzIndex(t)},visible:function(t){!1===t?this.hide():this.show()}},amapTagName:o}},methods:{__initComponent:function(t){this.$amapComponent=new AMap.BezierCurve(t)}}}},function(t,e,n){"use strict";var r=n(0),o="el-amap-circle-marker";e.a={name:o,mixins:[r.a],props:{vid:{type:String},zIndex:{type:Number},visible:{type:Boolean,default:!0},center:{type:Array,$type:"LngLat"},bubble:{type:Boolean},radius:{type:Number},strokeColor:{type:String},strokeOpacity:{type:Number},strokeWeight:{type:Number},fillColor:{type:String},fillOpacity:{type:Number},extData:{type:Object},events:{type:Object,default:function(){return{}}}},data:function(){return{converters:{},handlers:{zIndex:function(t){this.setzIndex(t)},visible:function(t){!1===t?this.hide():this.show()}},amapTagName:o}},methods:{__initComponent:function(t){this.$amapComponent=new AMap.CircleMarker(t)}}}},function(t,e,n){"use strict";var r=n(0),o="el-amap-ellipse";e.a={name:o,mixins:[r.a],props:{vid:{type:String},zIndex:{type:Number},center:{type:Array,$type:"LngLat"},radius:{type:Array,default:function(){return[1e3,1e3]}},bubble:{type:Boolean},cursor:{type:String},strokeColor:{type:String},strokeOpacity:{type:Number},strokeWeight:{type:Number},fillColor:{type:String},fillOpacity:{type:Number},strokeStyle:{type:String},extData:{type:Object,default:function(){return{}}},visible:{type:Boolean,default:!0},events:{type:Object,default:function(){return{}}}},data:function(){return{converters:{},handlers:{zIndex:function(t){this.setzIndex(t)},visible:function(t){!1===t?this.hide():this.show()}},amapTagName:o}},methods:{__initComponent:function(t){this.$amapComponent=new AMap.Ellipse(t)}}}},function(t,e,n){"use strict";var r=n(0),o="el-amap-rectangle";e.a={name:o,mixins:[r.a],props:{vid:{type:String},zIndex:{type:Number},center:{type:Array,$type:"LngLat"},bounds:{type:Array,$type:"Bounds"},bubble:{type:Boolean},cursor:{type:String},strokeColor:{type:String},strokeOpacity:{type:Number},strokeWeight:{type:Number},fillColor:{type:String},fillOpacity:{type:Number},strokeStyle:{type:String},extData:{type:Object,default:function(){return{}}},visible:{type:Boolean,default:!0},events:{type:Object,default:function(){return{}}}},data:function(){return{converters:{},handlers:{zIndex:function(t){this.setzIndex(t)},visible:function(t){!1===t?this.hide():this.show()}},amapTagName:o}},methods:{__initComponent:function(t){this.$amapComponent=new AMap.Rectangle(t)}}}},function(t,e,n){t.exports=n(27)},function(t,e,n){"use strict";var r=n(66);n.n(r)},function(t,e,n){n(67),n(68),n(81),n(84),t.exports=n(16).Map},function(t,e,n){"use strict";var r=n(28),o={};o[n(2)("toStringTag")]="z",o+""!="[object z]"&&n(7)(Object.prototype,"toString",function(){return"[object "+r(this)+"]"},!0)},function(t,e,n){"use strict";var r=n(69)(!0);n(23)(String,"String",function(t){this._t=String(t),this._i=0},function(){var t,e=this._t,n=this._i;return n>=e.length?{value:void 0,done:!0}:(t=r(e,n),this._i+=t.length,{value:t,done:!1})})},function(t,e,n){var r=n(21),o=n(22);t.exports=function(t){return function(e,n){var i,a,s=String(o(e)),u=r(n),c=s.length;return u<0||u>=c?t?"":void 0:(i=s.charCodeAt(u),i<55296||i>56319||u+1===c||(a=s.charCodeAt(u+1))<56320||a>57343?t?s.charAt(u):i:t?s.slice(u,u+2):a-56320+(i-55296<<10)+65536)}}},function(t,e){t.exports=!1},function(t,e){t.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},function(t,e,n){"use strict";var r=n(35),o=n(20),i=n(25),a={};n(8)(a,n(2)("iterator"),function(){return this}),t.exports=function(t,e,n){t.prototype=r(a,{next:o(1,n)}),i(t,e+" Iterator")}},function(t,e,n){var r=n(9),o=n(10),i=n(36);t.exports=n(5)?Object.defineProperties:function(t,e){o(t);for(var n,a=i(e),s=a.length,u=0;s>u;)r.f(t,n=a[u++],e[n]);return t}},function(t,e,n){var r=n(11),o=n(18),i=n(76)(!1),a=n(24)("IE_PROTO");t.exports=function(t,e){var n,s=o(t),u=0,c=[];for(n in s)n!=a&&r(s,n)&&c.push(n);for(;e.length>u;)r(s,n=e[u++])&&(~i(c,n)||c.push(n));return c}},function(t,e,n){var r=n(29);t.exports=Object("z").propertyIsEnumerable(0)?Object:function(t){return"String"==r(t)?t.split(""):Object(t)}},function(t,e,n){var r=n(18),o=n(37),i=n(77);t.exports=function(t){return function(e,n,a){var s,u=r(e),c=o(u.length),p=i(a,c);if(t&&n!=n){for(;c>p;)if((s=u[p++])!=s)return!0}else for(;c>p;p++)if((t||p in u)&&u[p]===n)return t||p||0;return!t&&-1}}},function(t,e,n){var r=n(21),o=Math.max,i=Math.min;t.exports=function(t,e){return t=r(t),t<0?o(t+e,0):i(t,e)}},function(t,e,n){var r=n(3).document;t.exports=r&&r.documentElement},function(t,e,n){var r=n(11),o=n(80),i=n(24)("IE_PROTO"),a=Object.prototype;t.exports=Object.getPrototypeOf||function(t){return t=o(t),r(t,i)?t[i]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?a:null}},function(t,e,n){var r=n(22);t.exports=function(t){return Object(r(t))}},function(t,e,n){for(var r=n(82),o=n(36),i=n(7),a=n(3),s=n(8),u=n(12),c=n(2),p=c("iterator"),f=c("toStringTag"),l=u.Array,d={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},h=o(d),v=0;v<h.length;v++){var m,y=h[v],g=d[y],b=a[y],_=b&&b.prototype;if(_&&(_[p]||s(_,p,l),_[f]||s(_,f,y),u[y]=l,g))for(m in r)_[m]||i(_,m,r[m],!0)}},function(t,e,n){"use strict";var r=n(83),o=n(39),i=n(12),a=n(18);t.exports=n(23)(Array,"Array",function(t,e){this._t=a(t),this._i=0,this._k=e},function(){var t=this._t,e=this._k,n=this._i++;return!t||n>=t.length?(this._t=void 0,o(1)):"keys"==e?o(0,n):"values"==e?o(0,t[n]):o(0,[n,t[n]])},"values"),i.Arguments=i.Array,r("keys"),r("values"),r("entries")},function(t,e,n){var r=n(2)("unscopables"),o=Array.prototype;void 0==o[r]&&n(8)(o,r,{}),t.exports=function(t){o[r][t]=!0}},function(t,e,n){"use strict";var r=n(85),o=n(44);t.exports=n(90)("Map",function(t){return function(){return t(this,arguments.length>0?arguments[0]:void 0)}},{get:function(t){var e=r.getEntry(o(this,"Map"),t);return e&&e.v},set:function(t,e){return r.def(o(this,"Map"),0===t?0:t,e)}},r,!0)},function(t,e,n){"use strict";var r=n(9).f,o=n(35),i=n(40),a=n(17),s=n(41),u=n(42),c=n(23),p=n(39),f=n(89),l=n(5),d=n(43).fastKey,h=n(44),v=l?"_s":"size",m=function(t,e){var n,r=d(e);if("F"!==r)return t._i[r];for(n=t._f;n;n=n.n)if(n.k==e)return n};t.exports={getConstructor:function(t,e,n,c){var p=t(function(t,r){s(t,p,e,"_i"),t._t=e,t._i=o(null),t._f=void 0,t._l=void 0,t[v]=0,void 0!=r&&u(r,n,t[c],t)});return i(p.prototype,{clear:function(){for(var t=h(this,e),n=t._i,r=t._f;r;r=r.n)r.r=!0,r.p&&(r.p=r.p.n=void 0),delete n[r.i];t._f=t._l=void 0,t[v]=0},delete:function(t){var n=h(this,e),r=m(n,t);if(r){var o=r.n,i=r.p;delete n._i[r.i],r.r=!0,i&&(i.n=o),o&&(o.p=i),n._f==r&&(n._f=o),n._l==r&&(n._l=i),n[v]--}return!!r},forEach:function(t){h(this,e);for(var n,r=a(t,arguments.length>1?arguments[1]:void 0,3);n=n?n.n:this._f;)for(r(n.v,n.k,this);n&&n.r;)n=n.p},has:function(t){return!!m(h(this,e),t)}}),l&&r(p.prototype,"size",{get:function(){return h(this,e)[v]}}),p},def:function(t,e,n){var r,o,i=m(t,e);return i?i.v=n:(t._l=i={i:o=d(e,!0),k:e,v:n,p:r=t._l,n:void 0,r:!1},t._f||(t._f=i),r&&(r.n=i),t[v]++,"F"!==o&&(t._i[o]=i)),t},getEntry:m,setStrong:function(t,e,n){c(t,e,function(t,n){this._t=h(t,e),this._k=n,this._l=void 0},function(){for(var t=this,e=t._k,n=t._l;n&&n.r;)n=n.p;return t._t&&(t._l=n=n?n.n:t._t._f)?"keys"==e?p(0,n.k):"values"==e?p(0,n.v):p(0,[n.k,n.v]):(t._t=void 0,p(1))},n?"entries":"values",!n,!0),f(e)}}},function(t,e,n){var r=n(10);t.exports=function(t,e,n,o){try{return o?e(r(n)[0],n[1]):e(n)}catch(e){var i=t.return;throw void 0!==i&&r(i.call(t)),e}}},function(t,e,n){var r=n(12),o=n(2)("iterator"),i=Array.prototype;t.exports=function(t){return void 0!==t&&(r.Array===t||i[o]===t)}},function(t,e,n){var r=n(28),o=n(2)("iterator"),i=n(12);t.exports=n(16).getIteratorMethod=function(t){if(void 0!=t)return t[o]||t["@@iterator"]||i[r(t)]}},function(t,e,n){"use strict";var r=n(3),o=n(9),i=n(5),a=n(2)("species");t.exports=function(t){var e=r[t];i&&e&&!e[a]&&o.f(e,a,{configurable:!0,get:function(){return this}})}},function(t,e,n){"use strict";var r=n(3),o=n(34),i=n(7),a=n(40),s=n(43),u=n(42),c=n(41),p=n(4),f=n(15),l=n(91),d=n(25),h=n(92);t.exports=function(t,e,n,v,m,y){var g=r[t],b=g,_=m?"set":"add",x=b&&b.prototype,C={},w=function(t){var e=x[t];i(x,t,"delete"==t?function(t){return!(y&&!p(t))&&e.call(this,0===t?0:t)}:"has"==t?function(t){return!(y&&!p(t))&&e.call(this,0===t?0:t)}:"get"==t?function(t){return y&&!p(t)?void 0:e.call(this,0===t?0:t)}:"add"==t?function(t){return e.call(this,0===t?0:t),this}:function(t,n){return e.call(this,0===t?0:t,n),this})};if("function"==typeof b&&(y||x.forEach&&!f(function(){(new b).entries().next()}))){var $=new b,M=$[_](y?{}:-0,1)!=$,E=f(function(){$.has(1)}),A=l(function(t){new b(t)}),O=!y&&f(function(){for(var t=new b,e=5;e--;)t[_](e,e);return!t.has(-0)});A||(b=e(function(e,n){c(e,b,t);var r=h(new g,e,b);return void 0!=n&&u(n,m,r[_],r),r}),b.prototype=x,x.constructor=b),(E||O)&&(w("delete"),w("has"),m&&w("get")),(O||M)&&w(_),y&&x.clear&&delete x.clear}else b=v.getConstructor(e,t,m,_),a(b.prototype,n),s.NEED=!0;return d(b,t),C[t]=b,o(o.G+o.W+o.F*(b!=g),C),y||v.setStrong(b,t,m),b}},function(t,e,n){var r=n(2)("iterator"),o=!1;try{var i=[7][r]();i.return=function(){o=!0},Array.from(i,function(){throw 2})}catch(t){}t.exports=function(t,e){if(!e&&!o)return!1;var n=!1;try{var i=[7],a=i[r]();a.next=function(){return{done:n=!0}},i[r]=function(){return a},t(i)}catch(t){}return n}},function(t,e,n){var r=n(4),o=n(93).set;t.exports=function(t,e,n){var i,a=e.constructor;return a!==n&&"function"==typeof a&&(i=a.prototype)!==n.prototype&&r(i)&&o&&o(t,i),t}},function(t,e,n){var r=n(4),o=n(10),i=function(t,e){if(o(t),!r(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,r){try{r=n(17)(Function.call,n(94).f(Object.prototype,"__proto__").set,2),r(t,[]),e=!(t instanceof Array)}catch(t){e=!0}return function(t,n){return i(t,n),e?t.__proto__=n:r(t,n),t}}({},!1):void 0),check:i}},function(t,e,n){var r=n(95),o=n(20),i=n(18),a=n(33),s=n(11),u=n(31),c=Object.getOwnPropertyDescriptor;e.f=n(5)?c:function(t,e){if(t=i(t),e=a(e,!0),u)try{return c(t,e)}catch(t){}if(s(t,e))return o(!r.f.call(t,e),t[e])}},function(t,e){e.f={}.propertyIsEnumerable},function(t,e,n){"use strict";t.exports=function(){var t=[].map.call(arguments,function(t){return t.trim()}).filter(function(t){return t.length}).join("-");return t.length?1!==t.length&&/[_.\- ]+/.test(t)?t.replace(/^[_.\- ]+/,"").toLowerCase().replace(/[_.\- ]+(\w|$)/g,function(t,e){return e.toUpperCase()}):t[0]===t[0].toLowerCase()&&t.slice(1)!==t.slice(1).toLowerCase()?t:t.toLowerCase():""}},function(t,e,n){"use strict";function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var o=n(98),i=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},a={key:null,v:"1.4.4",protocol:"https",hostAndPath:"webapi.amap.com/maps",plugin:[],callback:"amapInitComponent"},s=function(){function t(e){r(this,t),this._config=i({},a,e),this._document=document,this._window=window,this._scriptLoaded=!1,this._queueEvents=[o.a]}return t.prototype.load=function(){var t=this;if(this._window.AMap&&this._window.AMap.Map)return this.loadUIAMap();if(this._scriptLoadingPromise)return this._scriptLoadingPromise;var e=this._document.createElement("script");e.type="text/javascript",e.async=!0,e.defer=!0,e.src=this._getScriptSrc();var n=this._config.uiVersion?this.loadUIAMap():null;return this._scriptLoadingPromise=new Promise(function(r,o){t._window.amapInitComponent=function(){for(;t._queueEvents.length;)t._queueEvents.pop().apply();if(!n)return r();n.then(function(){window.initAMapUI(),setTimeout(r)})},e.onerror=function(t){return o(t)}}),this._document.head.appendChild(e),this._scriptLoadingPromise},t.prototype.loadUIAMap=function(){var t=this;return!this._config.uiVersion||window.AMapUI?Promise.resolve():new Promise(function(e,n){var r=document.createElement("script"),o=t._config.uiVersion.split("."),i=o[0],a=o[1],s=o[2];if(void 0===i||void 0===a)return void console.error("amap ui version is not correct, please check! version: ",t._config.uiVersion);var u=t._config.protocol+"://webapi.amap.com/ui/"+i+"."+a+"/main-async.js";s&&(u+="?v="+i+"."+a+"."+s),r.src=u,r.type="text/javascript",r.async=!0,t._document.head.appendChild(r),r.onload=function(){setTimeout(e,0)},r.onerror=function(){return n()}})},t.prototype._getScriptSrc=function(){var t=/^AMap./,e=this._config,n=["v","key","plugin","callback"];if(e.plugin&&e.plugin.length>0){e.plugin.push("Autocomplete","PlaceSearch","PolyEditor","CircleEditor");var r=[];e.plugin.forEach(function(e){var n=t.test(e)?e:"AMap."+e,o=n.replace(t,"");r.push(n,o)}),e.plugin=r}var o=Object.keys(e).filter(function(t){return~n.indexOf(t)}).filter(function(t){return null!=e[t]}).filter(function(t){return!Array.isArray(e[t])||Array.isArray(e[t])&&e[t].length>0}).map(function(t){var n=e[t];return Array.isArray(n)?{key:t,value:n.join(",")}:{key:t,value:n}}).map(function(t){return t.key+"="+t.value}).join("&");return this._config.protocol+"://"+this._config.hostAndPath+"?"+o},t}();e.a=s},function(t,e,n){"use strict";function r(){if(AMap.UA.ios&&"https:"!==document.location.protocol){var t=new o.a;navigator.geolocation.getCurrentPosition=function(){return t.getCurrentPosition.apply(t,arguments)},navigator.geolocation.watchPosition=function(){return t.watchPosition.apply(t,arguments)}}}e.a=r;var o=n(99)},function(t,e,n){"use strict";function r(){this._remoteSvrUrl="https://webapi.amap.com/html/geolocate.html",this._callbackList=[],this._seqBase=1,this._frameReady=0,this._watchIdMap={}}r.prototype={_getSeq:function(){return this._seqBase++},_onRrameReady:function(t){if(0===this._frameReady)return this._frameReadyList||(this._frameReadyList=[]),this._frameReadyList.push(t),void this._prepareIframe();t.call(this)},_prepareIframe:function(){if(!this._iframeWin){var t=document.createElement("iframe");t.src=this._remoteSvrUrl+(this._remoteSvrUrl.indexOf("?")>0?"&":"?"),t.width="0px",t.height="0px",t.style.position="absolute",t.style.display="none",t.allow="geolocation";var e=this,n=setTimeout(function(){e._frameReady=!1,e._callbackFrameReadyList()},5e3);t.onload=function(){clearTimeout(n),e._frameReady=!0,e._callbackFrameReadyList(),t.onload=null},document.body.appendChild(t),this._iframeWin=t.contentWindow,window.addEventListener("message",function(t){0===e._remoteSvrUrl.indexOf(t.origin)&&e._handleRemoteMsg(t.data)},!1)}},_callbackFrameReadyList:function(){if(this._frameReadyList){var t=this._frameReadyList;this._frameReadyList=null;for(var e=0,n=t.length;e<n;e++)t[e].call(this,this._frameReady)}},_pickCallback:function(t,e){for(var n=this._callbackList,r=0,o=n.length;r<o;r++){var i=n[r];if(t===i.seq)return e||n.splice(r,1),i}},_handleRemoteMsg:function(t){var e=t.seq,n=this._pickCallback(e,!!t.notify);n?n.cbk.call(null,t.error,t.result):console.warn("Receive remote msg: ",t)},_postMessage:function(t,e,n,r){this._prepareIframe();var o={cmd:t,args:e,seq:r||this._getSeq()};this._callbackList.push({cbk:n,seq:o.seq}),this._onRrameReady(function(){if(!0===this._frameReady)try{this._iframeWin.postMessage(o,"*")}catch(t){this._pickCallback(o.seq),n(t)}else this._pickCallback(o.seq),n({message:"iFrame load failed!"})})},getCurrentPosition:function(t,e,n){this._postMessage("getCurrentPosition",[n],function(n,r){if(n)return void(e&&e(n));t&&t(r)})},watchPosition:function(t,e,n){var r="wk"+this._getSeq(),o=this._getSeq();this._watchIdMap[r]={stat:0,seq:o};var i=this;return this._postMessage("watchPosition",[n],function(n,o){var a=null;o&&(a=o.id);var s=i._watchIdMap[r];if(s.id=a,s.stat=1,s.callbackList){var u=s.callbackList;s.callbackList=null;for(var c=0,p=u.length;c<p;c++)u[c].call(i,a)}if(n)return void(e&&e(n));t&&t(o.pos)},o),r},clearWatch:function(t,e){function n(n){o._postMessage("clearWatch",[n],function(n,i){n||(o._pickCallback(r.seq),delete o._watchIdMap[t]),e&&e(n,i)})}if(!this._watchIdMap[t])return void e("Id not exists: "+t);var r=this._watchIdMap[t],o=this;r.stat<1?(r.callbackList||(r.callbackList=[]),r.callbackList.push(function(t){n(t)})):n(r.id)}},e.a=r},function(t,e,n){"use strict";function r(t){n(101)}var o=n(48),i=n(105),a=n(1),s=r,u=Object(a.a)(o.a,i.a,i.b,!1,s,null,null);e.a=u.exports},function(t,e,n){var r=n(102);"string"==typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);n(47)("d6014b94",r,!0)},function(t,e,n){e=t.exports=n(46)(!1),e.push([t.i,".el-vue-amap-container,.el-vue-amap-container .el-vue-amap{height:100%}",""])},function(t,e){t.exports=function(t,e){for(var n=[],r={},o=0;o<e.length;o++){var i=e[o],a=i[0],s=i[1],u=i[2],c=i[3],p={id:t+":"+o,css:s,media:u,sourceMap:c};r[a]?r[a].parts.push(p):n.push(r[a]={id:a,parts:[p]})}return n}},function(t,e,n){"use strict";function r(){for(var t=[],e="0123456789abcdef",n=0;n<36;n++)t[n]=e.substr(Math.floor(16*Math.random()),1);return t[14]="4",t[19]=e.substr(3&t[19]|8,1),t[8]=t[13]=t[18]=t[23]="-",t.join("")}e.a=r},function(t,e,n){"use strict";n.d(e,"a",function(){return r}),n.d(e,"b",function(){return o});var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"el-vue-amap-container"},[n("div",{staticClass:"el-vue-amap"}),t._v(" "),t._t("default")],2)},o=[]},function(t,e,n){"use strict";var r=n(51),o=n(1),i=Object(o.a)(r.a,void 0,void 0,!1,null,null,null);e.a=i.exports},function(t,e,n){"use strict";function r(t){n(108)}var o=n(53),i=n(110),a=n(1),s=r,u=Object(a.a)(o.a,i.a,i.b,!1,s,null,null);e.a=u.exports},function(t,e,n){var r=n(109);"string"==typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);n(47)("80e271aa",r,!0)},function(t,e,n){e=t.exports=n(46)(!1),e.push([t.i,".el-vue-search-box-container{position:relative;width:360px;height:45px;background:#fff;box-shadow:0 2px 2px rgba(0,0,0,.15);border-radius:2px 3px 3px 2px;z-index:10}.el-vue-search-box-container .search-box-wrapper{position:absolute;display:flex;align-items:center;left:0;top:0;width:100%;height:100%;box-sizing:border-box}.el-vue-search-box-container .search-box-wrapper input{flex:1;height:20px;line-height:20px;letter-spacing:.5px;font-size:14px;text-indent:10px;box-sizing:border-box;border:none;outline:none}.el-vue-search-box-container .search-box-wrapper .search-btn{width:45px;height:100%;display:flex;align-items:center;justify-content:center;background:transparent;cursor:pointer}.el-vue-search-box-container .search-tips{position:absolute;top:100%;border:1px solid #dbdbdb;background:#fff;overflow:auto}.el-vue-search-box-container .search-tips ul{padding:0;margin:0}.el-vue-search-box-container .search-tips ul li{height:40px;line-height:40px;box-shadow:0 1px 1px rgba(0,0,0,.1);padding:0 10px;cursor:pointer}.el-vue-search-box-container .search-tips ul li.autocomplete-selected{background:#eee}",""])},function(t,e,n){"use strict";n.d(e,"a",function(){return r}),n.d(e,"b",function(){return o});var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"el-vue-search-box-container",on:{keydown:[function(e){if(!("button"in e)&&t._k(e.keyCode,"up",38,e.key,["Up","ArrowUp"]))return null;t.selectTip("up")},function(e){if(!("button"in e)&&t._k(e.keyCode,"down",40,e.key,["Down","ArrowDown"]))return null;t.selectTip("down")}]}},[n("div",{staticClass:"search-box-wrapper"},[n("input",{directives:[{name:"model",rawName:"v-model",value:t.keyword,expression:"keyword"}],attrs:{type:"text"},domProps:{value:t.keyword},on:{keyup:function(e){return"button"in e||!t._k(e.keyCode,"enter",13,e.key,"Enter")?t.search(e):null},input:[function(e){e.target.composing||(t.keyword=e.target.value)},t.autoComplete]}}),t._v(" "),n("span",{staticClass:"search-btn",on:{click:t.search}},[t._v("搜索")])]),t._v(" "),n("div",{staticClass:"search-tips"},[n("ul",t._l(t.tips,function(e,r){return n("li",{key:r,class:{"autocomplete-selected":r===t.selectedTip},on:{click:function(n){t.changeTip(e)},mouseover:function(e){t.selectedTip=r}}},[t._v(t._s(e.name))])}))])])},o=[]},function(t,e,n){"use strict";var r=n(54),o=n(112),i=n(1),a=Object(i.a)(r.a,o.a,o.b,!1,null,null,null);e.a=a.exports},function(t,e,n){"use strict";n.d(e,"a",function(){return r}),n.d(e,"b",function(){return o});var r=function(){var t=this,e=t.$createElement;return(t._self._c||e)("div")},o=[]},function(t,e,n){"use strict";var r=n(55),o=n(114),i=n(1),a=Object(i.a)(r.a,o.a,o.b,!1,null,null,null);e.a=a.exports},function(t,e,n){"use strict";n.d(e,"a",function(){return r}),n.d(e,"b",function(){return o});var r=function(){var t=this,e=t.$createElement;return(t._self._c||e)("div")},o=[]},function(t,e,n){"use strict";var r=n(56),o=n(1),i=Object(o.a)(r.a,void 0,void 0,!1,null,null,null);e.a=i.exports},function(t,e,n){"use strict";var r=n(57),o=n(117),i=n(1),a=Object(i.a)(r.a,o.a,o.b,!1,null,null,null);e.a=a.exports},function(t,e,n){"use strict";n.d(e,"a",function(){return r}),n.d(e,"b",function(){return o});var r=function(){var t=this,e=t.$createElement;return(t._self._c||e)("div")},o=[]},function(t,e,n){"use strict";var r=n(58),o=n(119),i=n(1),a=Object(i.a)(r.a,o.a,o.b,!1,null,null,null);e.a=a.exports},function(t,e,n){"use strict";n.d(e,"a",function(){return r}),n.d(e,"b",function(){return o});var r=function(){var t=this,e=t.$createElement;return(t._self._c||e)("div")},o=[]},function(t,e,n){"use strict";var r=n(59),o=n(121),i=n(1),a=Object(i.a)(r.a,o.a,o.b,!1,null,null,null);e.a=a.exports},function(t,e,n){"use strict";n.d(e,"a",function(){return r}),n.d(e,"b",function(){return o});var r=function(){var t=this,e=t.$createElement;return(t._self._c||e)("div")},o=[]},function(t,e,n){"use strict";var r=n(60),o=n(123),i=n(1),a=Object(i.a)(r.a,o.a,o.b,!1,null,null,null);e.a=a.exports},function(t,e,n){"use strict";n.d(e,"a",function(){return r}),n.d(e,"b",function(){return o});var r=function(){var t=this,e=t.$createElement;return(t._self._c||e)("div")},o=[]},function(t,e,n){"use strict";var r=n(61),o=n(125),i=n(1),a=Object(i.a)(r.a,o.a,o.b,!1,null,null,null);e.a=a.exports},function(t,e,n){"use strict";n.d(e,"a",function(){return r}),n.d(e,"b",function(){return o});var r=function(){var t=this,e=t.$createElement;return(t._self._c||e)("div")},o=[]},function(t,e,n){"use strict";var r=n(62),o=n(127),i=n(1),a=Object(i.a)(r.a,o.a,o.b,!1,null,null,null);e.a=a.exports},function(t,e,n){"use strict";n.d(e,"a",function(){return r}),n.d(e,"b",function(){return o});var r=function(){var t=this,e=t.$createElement;return(t._self._c||e)("div")},o=[]},function(t,e,n){"use strict";var r=n(63),o=n(129),i=n(1),a=Object(i.a)(r.a,o.a,o.b,!1,null,null,null);e.a=a.exports},function(t,e,n){"use strict";n.d(e,"a",function(){return r}),n.d(e,"b",function(){return o});var r=function(){var t=this,e=t.$createElement;return(t._self._c||e)("div")},o=[]},function(t,e,n){"use strict";function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var o=function(){function t(){r(this,t),this._componentMap=new Map,this._map=null}return t.prototype.setMap=function(t){this._map=t},t.prototype.getMap=function(){return this._map},t.prototype.setComponent=function(t,e){this._componentMap.set(t,e)},t.prototype.getComponent=function(t){return this._componentMap.get(t)},t.prototype.getChildInstance=function(t){return this.getComponent(t)},t.prototype.removeComponent=function(t){this._componentMap.delete(t)},t}();e.a=o},function(t,e,n){"use strict";var r=n(0),o=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t};e.a=function(t){var e=t.init,n=t.data,i=void 0===n?function(){return{}}:n,a=t.converters,s=void 0===a?{}:a,u=t.handlers,c=void 0===u?{}:u,p=t.computed,f=t.methods,l=t.name,d=t.render,h=t.contextReady,v=t.template,m=t.mixins,y=void 0===m?[]:m,g=t.props,b=void 0===g?{}:g,_=o({},t,{props:b,data:function(){return o({},i(),{converters:s,handlers:c})},mixins:[r.a].concat(y),computed:p,methods:o({},f,{__initComponent:e,__contextReady:h})});return v||d||(_.render=function(){return null}),_.install=function(t){return t.use(l,_)},_}}])});
//# sourceMappingURL=index.js.map