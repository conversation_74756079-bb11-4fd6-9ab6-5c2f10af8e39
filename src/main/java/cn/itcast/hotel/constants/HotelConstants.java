package cn.itcast.hotel.constants;

public class HotelConstants {
    public static final String MAPPING_TEMPLATE = "{\n" +
            "  \"mappings\": {\n" +
            "    \"properties\": {\n" +
            "      \"id\": {\"type\": \"keyword\"},\n" +
            "      \"name\": {\"type\": \"text\", \"analyzer\": \"ik_max_word\", \"copy_to\": \"search_all\"},\n" +
            "      \"address\": {\"type\": \"keyword\", \"index\": false},\n" +
            "      \"price\": {\"type\": \"integer\"},\n" +
            "      \"score\": {\"type\": \"integer\"},\n" +
            "      \"brand\": {\"type\": \"keyword\", \"copy_to\": \"search_all\"},\n" +
            "      \"city\": {\"type\": \"keyword\", \"copy_to\": \"search_all\"},\n" +
            "      \"starName\": {\"type\": \"keyword\"},\n" +
            "      \"business\": {\"type\": \"keyword\"},\n" +
            "      \"location\": {\"type\": \"geo_point\"},\n" +
            "      \"pic\": {\"type\": \"keyword\", \"index\": false},\n" +
            "      \"search_all\": {\"type\": \"text\", \"analyzer\": \"ik_max_word\"}\n" +
            "    }\n" +
            "  }\n" +
            "}";
}
